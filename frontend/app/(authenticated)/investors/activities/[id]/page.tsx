'use client';

import useActivityReadMutation from '@/api/hooks/mutations/useActivityReadMutation';
import useArchiveActivityMutation from '@/api/hooks/mutations/useArchiveActivityMutation';
import useFlagActivityMutation from '@/api/hooks/mutations/useFlagActivityMutation';
import useActivity from '@/api/hooks/useActivity';
import { isChatter } from '@/api/hooks/useChatter';
import ActivityFilesList from '@/components/ActivityFilesList';
import ActivitiesManagerContext from '@/app/(authenticated)/investors/activities/ActivitiesManagerContext';
import ActivityAction from '@/components/ActivityAction';
import ActivityDetailsCard from '@/components/ActivityDetailsCard';
import ActivityThread from '@/components/ActivityThread';
import ChangeIndicator from '@/components/ChangeIndicator';
import { useOrganisation } from '@/components/OrganisationProvider';
import { formatWithTimeZone } from '@/util/date';
import { useSentimentBands } from '@/util/sentiment';
import useActivitySummary from '@/util/useActivitySummary';
import {
    EnvelopeIcon,
    EnvelopeOpenIcon,
    FlagIcon as FlagIconOutline,
    LinkIcon,
    TrashIcon as TrashIconOutline
} from '@heroicons/react/24/outline';
import {
    FlagIcon as FlagIconSolid,
    TrashIcon as TrashIconSolid
} from '@heroicons/react/24/solid';
import { useContext, useMemo } from 'react';
import { useActivityFiles } from '@/api/hooks/useActivityFiles';

export default function ActivityPage({ params }: { params: { id: string } }) {
    const organisation = useOrganisation();

    const { data: activity } = useActivity(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        params.id
    );

    const { files } = useActivityFiles(params.id);

    const { timeSeriesByDate, indexSeriesByDate, activitiesByDay, risk } = useContext(
        ActivitiesManagerContext
    );
    const { url } = useActivitySummary(activity);

    const day = useMemo(() => {
        return activity && formatWithTimeZone(activity.posted, 'yyyy-MM-dd');
    }, [activity]);

    const quote = useMemo(() => {
        if (day !== undefined && timeSeriesByDate !== undefined) {
            return timeSeriesByDate?.[day]?.[0];
        } else {
            return undefined;
        }
    }, [day, timeSeriesByDate]);

    const previousDayQuote = useMemo(() => {
        const timeSeries = Object.values(timeSeriesByDate ?? {})
            .flatMap((it) => it[0])
            ?.reverse();

        const dayIndex = timeSeries.findIndex((it) => it.datetime === day);
        return timeSeries[dayIndex + 1] ?? undefined;
    }, [day, timeSeriesByDate]);

    const changeIndicator = useMemo(() => {
        if (quote && previousDayQuote && previousDayQuote.close > 0) {
            const logReturn = Math.log(quote.close / previousDayQuote.close);
            const percentage = logReturn * 100;
            const sign = percentage > 0 ? '+' : percentage < 0 ? '-' : '';
            return (
                <ChangeIndicator
                    value={logReturn}
                    label={`${Math.abs(logReturn).toFixed(2)} (${sign}${Math.abs(
                        percentage
                    ).toFixed(2)}%)`}
                />
            );
        } else {
            return undefined;
        }
    }, [quote, previousDayQuote]);

    const index = useMemo(() => {
        if (day !== undefined && indexSeriesByDate !== undefined) {
            return indexSeriesByDate?.[day]?.[0];
        } else {
            return undefined;
        }
    }, [day, indexSeriesByDate]);

    const previousIndex = useMemo(() => {
        const indexSeries = Object.values(indexSeriesByDate ?? {})
            .flatMap((it) => it[0])
            ?.reverse();
        const dayIndex = indexSeries.findIndex((it) => it.datetime === day);
        return indexSeries[dayIndex + 1] ?? undefined;
    }, [day, indexSeriesByDate]);

    const abnormal = useMemo(() => {
        if (
            quote &&
            previousDayQuote &&
            index &&
            previousIndex &&
            risk &&
            previousDayQuote.close > 0 &&
            previousIndex.close > 0
        ) {
            const stockLogReturn = Math.log(quote.close / previousDayQuote.close);
            const indexLogReturn = Math.log(index.close / previousIndex.close);
            const expectedReturn = risk.intercept + risk.slope * indexLogReturn;
            const abnormalReturn = stockLogReturn - expectedReturn;
            return abnormalReturn;
        } else {
            return undefined;
        }
    }, [quote, previousDayQuote, index, risk, previousIndex]);

    const todaysChatter = useMemo(() => {
        if (day && activitiesByDay && activitiesByDay[day]) {
            return activitiesByDay[day].filter((it) => isChatter(it));
        } else {
            return [];
        }
    }, [day, activitiesByDay]);

    const sentimentBands = useSentimentBands(todaysChatter);

    // TODO: add followers & mailchimp subscribers
    const stats: Array<[string, JSX.Element | string, JSX.Element | undefined]> = [
        ...(quote !== undefined
            ? [
                ['Share price (Open)', `$${quote.open.toFixed(4)}`, undefined] as [
                    string,
                    string,
                    undefined
                ],
                [
                    'Day range',
                    `$${quote.low.toFixed(4)} - $${quote.high.toFixed(4)}`,
                    undefined
                ] as [string, string, undefined],
                [
                    'Share price (close)',
                    `$${quote.close.toFixed(4)}`,
                    changeIndicator
                ] as [string, string, undefined],
                ['Volume traded', `${quote.volume.toLocaleString()}`, undefined] as [
                    string,
                    string,
                    undefined
                ]
            ]
            : []),
        ...(abnormal !== undefined
            ? [
                [
                    'Abnormal return',
                    <ChangeIndicator
                        value={abnormal}
                        key={abnormal}
                        label={`${(Math.abs(abnormal) * 100).toFixed(2)}%`}
                    />,
                    undefined
                ] as [string, JSX.Element, undefined]
            ]
            : [])
    ];

    const threadId: string | undefined = useMemo(() => {
        switch (activity?.type) {
            case 'hotcopper':
                return activity?.thread?.thread ?? undefined;

            case 'reddit':
            case 'redditComment':
                return activity.post;

            case 'tweet':
            case 'linkedIn':
                return activity?.thread ?? undefined;

            default:
                return undefined;
        }
    }, [activity]);

    const { read, unread } = useActivityReadMutation();
    const { flag, unflag } = useFlagActivityMutation(organisation.selected?.organisation);
    const { archive, unarchive } = useArchiveActivityMutation(
        organisation.selected?.organisation
    );

    if (!activity) {
        return (
            <div className="p-4 flex flex-col gap-y-6 bg-gray-100 flex-1 h-full vh-100" />
        );
    }

    return (
        <div className="p-4 flex flex-col  bg-gray-100 flex-1 h-full">
            <div className="mt-1 p-4 flex flex-col border border-gray-200 rounded-lg bg-white gap-y-6 shadow-md">
                {activity?.posted && (
                    <h1 className="font-medium text-xl">
                        {formatWithTimeZone(activity.posted, 'd MMMM yyyy')}
                    </h1>
                )}
                <div className="overflow-hidden flex rounded-md">
                    <div
                        className="h-2 bg-red-500"
                        style={{ width: `${(sentimentBands[0] * 100).toFixed(2)}%` }}
                    />
                    <div
                        className="h-2 bg-yellow-300"
                        style={{ width: `${(sentimentBands[1] * 100).toFixed(2)}%` }}
                    />
                    <div
                        className="h-2 bg-slate-300"
                        style={{ width: `${(sentimentBands[2] * 100).toFixed(2)}%` }}
                    />
                    <div
                        className="h-2 bg-green-400"
                        style={{ width: `${(sentimentBands[3] * 100).toFixed(2)}%` }}
                    />
                </div>
                <dl>
                    {stats.length > 0 ? (
                        stats.map(([label, value, changeIndicator]) => (
                            <div
                                key={label}
                                className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0" // Changed py-1 to py-2
                            >
                                <dt className="text-sm font-medium text-gray-900">
                                    {label}
                                </dt>
                                <dd className="mt-1 text-sm text-right text-gray-700 sm:col-span-2 sm:mt-0 flex items-center justify-end gap-2">
                                    <span>{value}</span>
                                    {changeIndicator}
                                </dd>
                            </div>
                        ))
                    ) : (
                        <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                            <dt className="text-sm font-medium text-gray-900">
                                Market closed
                            </dt>
                        </div>
                    )}
                </dl>
            </div>

            <div className="flex flex-col gap-y-1 mt-6">
                <div className="flex flex-row justify-between items-center">
                    <dt className="text-sm font-semibold text-gray-500">Activity</dt>
                    <dt className="text-sm font-semibold text-gray-500">
                        <div className="flex items-center gap-x-2 ">
                            <ActivityAction
                                icon={activity?.read ? EnvelopeOpenIcon : EnvelopeIcon}
                                tooltip={`Mark as ${activity?.read ? 'unread' : 'read'}`}
                                onClick={async () => {
                                    if (activity?.read) {
                                        await unread(
                                            new URLSearchParams({ id: params.id })
                                        );
                                    } else {
                                        await read([{ activity: params.id }]);
                                    }
                                }}
                            />
                            <ActivityAction
                                tooltip={`${activity?.flagged ? 'Un-flag' : 'Flag'} activity`}
                                icon={activity?.flagged ? FlagIconSolid : FlagIconOutline}
                                onClick={async () => {
                                    if (activity?.flagged) {
                                        await unflag(
                                            new URLSearchParams({ id: params.id })
                                        );
                                    } else {
                                        await flag([{ activity: params.id }]);
                                    }
                                }}
                            />
                            <ActivityAction
                                tooltip={`${activity?.archived ? 'Un-archive' : 'Archive'} activity`}
                                icon={
                                    activity?.archived ? TrashIconSolid : TrashIconOutline
                                }
                                onClick={async () => {
                                    if (activity?.archived) {
                                        await unarchive(
                                            new URLSearchParams({ id: params.id })
                                        );
                                    } else {
                                        await archive([{ activity: params.id }]);
                                    }
                                }}
                            />
                        </div>
                    </dt>
                </div>
            </div>
            <div className="mt-1 flex flex-col border border-gray-200  bg-white">
                {!threadId ? (
                    <ActivityDetailsCard activity={activity} imgClassNames="-mx-8" />
                ) : null}

                {threadId ? (
                    <ActivityThread threadId={threadId} selectedActivity={activity?.id} />
                ) : null}
            </div>

            {files?.length > 0 && (
                <>
                    <dt className="text-sm font-semibold text-gray-500 mt-6">
                        Attachments
                    </dt>
                    <div className="mt-3 border border-gray-200 bg-white p-4 rounded-md">
                        <ActivityFilesList files={files} />
                    </div>
                </>
            )}

            <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline flex items-center text-sm font-medium text-indigo-600 gap-x-1 py-4">
                <LinkIcon className="size-4" />
                <span>Open in new tab</span>
            </a>
        </div>
    );
}
