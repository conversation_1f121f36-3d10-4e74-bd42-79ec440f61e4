import { Sentiment } from '@quarterback/types';
import { DiscreteSentiment, discreteSentiment, sentimentScore } from '@/util/sentiment';
import React, { useMemo } from 'react';
import classNames from 'classnames';
import { FaceFrownIcon, FaceSmileIcon } from '@heroicons/react/24/outline';

export default function SentimentIndicator({ sentiment }: { sentiment: Sentiment }) {
    const score = sentimentScore(sentiment);
    const discrete = discreteSentiment(score);

    const colors = useMemo(() => {
        if (discrete === DiscreteSentiment.NEGATIVE)
            return ['bg-red-50', 'text-red-700', 'ring-red-600/10'];
        if (discrete === DiscreteSentiment.LACKING)
            return ['bg-yellow-50', 'text-yellow-700', 'ring-yellow-600/10'];
        if (discrete === DiscreteSentiment.POSITIVE)
            return ['bg-green-50', 'text-green-700', 'ring-green-600/10'];
        if (discrete === DiscreteSentiment.NEUTRAL)
            return ['bg-slate-50', 'text-slate-700', 'ring-slate-600/10'];

        return [];
    }, [discrete]);

    return (
        <div
            className={classNames(
                'inline-flex gap-x-1 items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset',
                ...colors
            )}>
            <span>{score.toFixed(2)}</span>
            {score < 0 ? (
                <FaceFrownIcon className="size-4" />
            ) : (
                <FaceSmileIcon className="size-4" />
            )}
        </div>
    );
}
