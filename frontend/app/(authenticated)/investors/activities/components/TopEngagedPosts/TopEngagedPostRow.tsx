import useActivitySummary from '@/util/useActivitySummary';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import { Activity } from '@quarterback/types';
import { useMemo } from 'react';
import { truncate } from '@quarterback/util';

export default function TopEngagedPostRow({ activity }: { activity: Activity }) {
    const {
        title,
        description,
        author,
        engagement: activityEngagement
    } = useActivitySummary(activity);

    const truncatedAuthor = useMemo(() => {
        if (author === 'Australian Securities Exchange') return 'ASX';
        return truncate(author, 20);
    }, [author]);

    const displayText = useMemo(() => {
        return title || description || 'No content available';
    }, [title, description]);

    const truncatedText = useMemo(() => {
        return truncate(displayText, 60);
    }, [displayText]);

    return (
        <tr className="hover:bg-gray-50">
            <td className="py-3 px-2">
                <div className="flex items-center gap-x-2 min-w-0">
                    <ActivitySourceIcon
                        className="rounded-md size-5 object-cover flex-shrink-0"
                        activity={activity}
                    />
                    <span className="text-xs font-medium text-gray-900 truncate">
                        {truncatedAuthor}
                    </span>
                </div>
            </td>
            <td className="py-3 px-4">
                <div className="text-sm text-gray-900 line-clamp-2 break-words">
                    {truncatedText}
                </div>
            </td>
            <td className="py-3 px-2 text-right">
                <span className="text-sm font-medium text-gray-900">
                    {activityEngagement ?? 0}
                </span>
            </td>
        </tr>
    );
}
