import { Activity } from '@quarterback/types';
import React, { useMemo } from 'react';
import ActivitySourceIcon from '@/components/ActivitySourceIcon';
import { sentimentScore, DiscreteSentiment, discreteSentiment } from '@/util/sentiment';
import hasField from '@/util/hasField';
import { author as getAuthor } from '@/util/author';

interface AuthorStats {
    author: string;
    postCount: number;
    averageSentiment: number;
    activities: Activity[];
    sampleActivity: Activity; // For getting the source icon
}

export default function People({ activities }: { activities: Array<Activity> }) {
    const authorStats = useMemo(() => {
        // Group activities by author
        const authorMap = new Map<string, Activity[]>();

        activities.forEach((activity) => {
            // Use the same logic as useActivitySummary to get consistent author names
            const author = getAuthor(activity);

            if (author) {
                if (!authorMap.has(author)) {
                    authorMap.set(author, []);
                }
                authorMap.get(author)!.push(activity);
            }
        });

        // Calculate stats for each author
        const stats: AuthorStats[] = [];
        authorMap.forEach((authorActivities, author) => {
            // Calculate average sentiment for this author
            const sentimentActivities = authorActivities.filter(hasField('sentiment'));
            let averageSentiment = 0;

            if (sentimentActivities.length > 0) {
                const totalSentiment = sentimentActivities.reduce(
                    (sum, activity) => sum + sentimentScore(activity.sentiment),
                    0
                );
                averageSentiment = totalSentiment / sentimentActivities.length;
            }

            stats.push({
                author,
                postCount: authorActivities.length,
                averageSentiment,
                activities: authorActivities,
                sampleActivity: authorActivities[0]
            });
        });

        return stats.sort((a, b) => b.postCount - a.postCount).slice(0, 5);
    }, [activities]);

    const getSentimentColor = (sentiment: number) => {
        const discrete = discreteSentiment(sentiment);
        switch (discrete) {
            case DiscreteSentiment.NEGATIVE:
                return '#dc2626'; // red-600
            case DiscreteSentiment.LACKING:
                return '#ca8a04'; // yellow-600
            case DiscreteSentiment.NEUTRAL:
                return '#475569'; // slate-200
            case DiscreteSentiment.POSITIVE:
                return '#16a34a'; // green-300
            default:
                return '#e2e8f0';
        }
    };

    if (authorStats.length === 0) {
        return (
            <div className="text-center text-gray-500 py-8 text-sm">
                No author data available
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4 text-sm font-medium text-gray-500 border-b border-gray-200 pb-2 ">
                <div>Author</div>
                <div className="text-center">Number of posts</div>
                <div className="text-center">Average sentiment</div>
            </div>

            <div className="space-y-3">
                {authorStats.map((stats, index) => (
                    <div
                        key={`${stats.author}-${index}`}
                        className="grid grid-cols-3 gap-4 items-center py-2">
                        <div className="flex items-center gap-2">
                            <ActivitySourceIcon
                                activity={stats.sampleActivity}
                                className="size-5 rounded-md flex-shrink-0"
                            />
                            <span className="text-sm font-medium text-gray-900 truncate">
                                {stats.author}
                            </span>
                        </div>

                        <div className="text-center text-sm font-medium text-gray-900">
                            {stats.postCount}
                        </div>

                        {/* Average sentiment */}
                        <div className="text-center">
                            <span
                                className="text-sm font-medium"
                                style={{
                                    color: getSentimentColor(stats.averageSentiment)
                                }}>
                                {stats.averageSentiment.toFixed(2)}
                            </span>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
