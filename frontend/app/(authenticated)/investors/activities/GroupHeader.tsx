import React, { useMemo } from 'react';
import { Activity, ListedEntity } from '@quarterback/types';
import { TimeSeriesQuote } from '@/api/hooks/useTimeSeries';
import { isDefined } from '@quarterback/util';
import { sentimentScore } from '@/util/sentiment';
import {
    ChevronDownIcon,
    ChevronUpIcon,
    FaceFrownIcon,
    FaceSmileIcon,
    HandThumbUpIcon
} from '@heroicons/react/24/outline';
import Tooltip from '@/components/Tooltip';
import classNames from 'classnames';
import ChangeIndicator from '@/components/ChangeIndicator';
import { formatInTimeZone } from 'date-fns-tz';

export default function GroupHeader({
    collapsed,
    setCollapsed,
    group,
    entity,
    quote,
    index,
    risk
}: {
    collapsed: boolean;
    setCollapsed: React.Dispatch<React.SetStateAction<boolean>>;
    group: { name: string; datetime: Date; activities: Array<Activity> };
    entity: ListedEntity | undefined;
    quote: {
        current: TimeSeriesQuote | undefined;
        previous: TimeSeriesQuote | undefined;
    };
    index: {
        current: TimeSeriesQuote | undefined;
        previous: TimeSeriesQuote | undefined;
    };
    risk: { slope: number; intercept: number; r2: number } | undefined;
}) {
    const engagement = useMemo(() => {
        return group.activities
            .map((it) => {
                if (
                    it.type === 'tweet' ||
                    it.type === 'hotcopper' ||
                    it.type === 'linkedIn'
                ) {
                    return it.likes;
                }

                if (it.type === 'reddit' || it.type === 'redditComment') {
                    return it.score;
                }

                return undefined;
            })
            .filter(isDefined)
            .reduce((sum, engagement) => {
                return sum + engagement;
            }, 0);
    }, [group]);

    const sentiment = useMemo(() => {
        const withSentiment = group.activities
            .map((it) => it.sentiment)
            .filter(isDefined);
        const aggSentiment = withSentiment.reduce(
            (agg, sentiment) => {
                return {
                    sum: agg.sum + sentimentScore(sentiment),
                    count: agg.count + 1
                };
            },
            { sum: 0, count: 0 }
        );

        if (withSentiment.length) {
            return aggSentiment.sum / aggSentiment.count;
        } else {
            return undefined;
        }
    }, [group]);

    const abnormal = useMemo(() => {
        if (quote && index && risk) {
            const change =
                quote.current && quote.previous
                    ? Math.log(quote.current.close / quote.previous.close)
                    : 0;

            const indexChange =
                index.current && index.previous
                    ? Math.log(index.current.close / index.previous.close)
                    : 0;

            return change - (risk.intercept + risk.slope * indexChange);
        } else {
            return undefined;
        }
    }, [quote, risk, index]);

    const [label, value] = useMemo(() => {
        if (quote.current && quote.previous) {
            const value = Math.log(quote.current.close / quote.previous.close);
            const percentage = Math.log(quote.current.close / quote.previous.close) * 100;
            const sign = percentage > 0 ? '+' : percentage < 0 ? '-' : '';

            const label = `${Math.abs(value).toFixed(4)} (${sign}${Math.abs(
                percentage
            ).toFixed(2)}%)`;
            return [label, value];
        }
        return [];
    }, [quote]);

    const sharePriceText = useMemo(() => {
        const formatDate = (date: Date) =>
            formatInTimeZone(date, 'Australia/Sydney', 'd MMMM, yyyy');
        const formatTime = (date: Date) =>
            formatInTimeZone(date, 'Australia/Sydney', 'HH:mm');

        const groupDateString = formatDate(group.datetime);
        const todayDateString = formatDate(new Date());
        const timeString = formatTime(new Date());

        const isToday = groupDateString === todayDateString;
        const isAfter4PM = parseInt(timeString.split(':')[0], 10) >= 16;

        const isWeekend = [0, 6].includes(group.datetime.getDay());
        const isMarketClosed = isWeekend || !isToday || (isToday && isAfter4PM);

        return isMarketClosed ? 'Share price at close' : 'Current share price';
    }, [group.datetime]);

    return (
        <tr className="border-t border-gray-200">
            <th
                colSpan={2}
                scope="colgroup"
                className="bg-gray-50 py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                <div
                    className="flex items-center gap-x-2 cursor-pointer"
                    onClick={() => setCollapsed((collapsed) => !collapsed)}>
                    {collapsed ? (
                        <ChevronDownIcon className="size-4" />
                    ) : (
                        <ChevronUpIcon className="size-4" />
                    )}
                    <span>{group.name}</span>
                </div>
            </th>
            <th
                colSpan={1}
                scope="colgroup"
                className="bg-gray-50 py-2 pl-4 pr-3 text-right text-sm font-semibold text-gray-900 sm:pl-3">
                <div className="flex items-center justify-end gap-x-2">
                    {quote.current && (
                        <>
                            <Tooltip text={sharePriceText}>
                                <div className="flex items-center justify-end gap-x-2 cursor-help">
                                    <span className="text-gray-400 text-xs">
                                        Share Price (Close)
                                    </span>
                                    <span className={classNames('font-semibold text-xs')}>
                                        ${(quote?.current?.close ?? 0).toFixed(4)}
                                    </span>
                                    <span
                                        className={classNames('text-xs font-semibold', {
                                            'text-red-600': value && value < 0,
                                            'text-green-600': value && value > 0
                                        })}>
                                        {label}
                                    </span>
                                </div>
                            </Tooltip>
                            {abnormal ? (
                                <Tooltip text="24h abnormal return">
                                    <div className="flex items-center justify-end gap-x-2 cursor-help ml-3">
                                        <span className=" text-gray-400 text-xs">
                                            ARR
                                        </span>
                                        <span className="font-semibold text-xs">
                                            {' '}
                                            AXJO
                                        </span>
                                        <span
                                            className={classNames(
                                                'text-xs font-semibold',
                                                {
                                                    'text-red-600': abnormal < 0,
                                                    'text-green-600': abnormal > 0
                                                }
                                            )}>
                                            {(abnormal * 100).toFixed(2)}%
                                        </span>
                                    </div>
                                </Tooltip>
                            ) : undefined}
                            <Tooltip text={'Volume traded'}>
                                <div className="flex items-center justify-end gap-x-2 cursor-help ml-3">
                                    <span className="text-gray-400 text-xs">VOLUME</span>
                                    <span className={classNames('font-semibold text-xs')}>
                                        {quote?.current?.volume.toLocaleString()}
                                    </span>
                                </div>
                            </Tooltip>
                        </>
                    )}
                </div>
            </th>
            <th
                colSpan={1}
                className="bg-gray-50 py-2 pl-4 pr-3 text-left text-sm text-gray-900 sm:pl-3">
                <div className="flex items-center gap-x-2" />
            </th>

            <th
                colSpan={1}
                className="top-0 bg-gray-50 py-2 pl-4 pr-3 text-right font-normal text-gray-900 sm:pl-3">
                {sentiment !== undefined && (
                    <div className="flex items-center gap-x-2">
                        <span className="text-sm">{(sentiment ?? 0).toFixed(2)}</span>
                        {sentiment >= 0 ? (
                            <FaceSmileIcon className="size-4" />
                        ) : (
                            <FaceFrownIcon className="size-4" />
                        )}
                    </div>
                )}
            </th>
        </tr>
    );
}
