import ContentCard from '@/components/cards/ContentCard';
import InitialColors from '@/util/InitialColors';
import { useSentimentBands } from '@/util/sentiment';
import { ArrowDownIcon, ArrowUpIcon, ChevronRightIcon } from '@heroicons/react/16/solid';
import { Activity, Followers } from '@quarterback/types';
import { isDefined } from '@quarterback/util';
import classNames from 'classnames';
import { useMemo } from 'react';

export default function Chatter({
    media,
    chatter,
    followers,
    className
}: {
    media: Array<Activity>;
    chatter: Array<Activity>;
    followers: Followers;
    className?: string;
}) {
    const authors = useMemo(() => {
        return new Set(
            chatter
                .map((activity) => {
                    switch (activity.type) {
                        case 'hotcopper':
                        case 'tweet':
                        case 'reddit':
                        case 'redditComment':
                            return activity.author?.userId;
                        case 'linkedIn':
                            return activity.author?.name;
                        case 'news':
                            return activity.source.name;
                        default:
                            return undefined;
                    }
                })
                .filter(isDefined)
        );
    }, [chatter]);

    const { now: currentFollowers, previous: previousFollowers } = useMemo(() => {
        return {
            now:
                +(followers.twitter?.[0]?.followers ?? 0) +
                +(followers.linkedIn?.[0]?.followers ?? 0),
            previous:
                +(
                    followers?.twitter?.[1]?.followers ??
                    followers?.twitter?.[0]?.followers ??
                    0
                ) +
                +(
                    followers?.linkedIn?.[1]?.followers ??
                    followers?.linkedIn?.[0]?.followers ??
                    0
                )
        };
    }, [followers?.linkedIn, followers?.twitter]);

    const followersChange = useMemo(() => {
        const difference = currentFollowers - previousFollowers;
        if (difference !== 0) {
            return (difference / previousFollowers || 1) * 100;
        }

        return 0;
    }, [currentFollowers, previousFollowers]);

    const [red, yellow, gray, green] = useSentimentBands(chatter);

    return (
        <div className={classNames('grid grid-cols-6 gap-4 ')}>
            <ContentCard
                className="col-span-1"
                title="Activities"
                initialBadge={{
                    color: InitialColors.ACTIVITY
                }}>
                <div className="text-3xl">{chatter.length}</div>
            </ContentCard>
            <ContentCard
                className="col-span-1"
                title="Media"
                initialBadge={{
                    color: InitialColors.MEDIA
                }}>
                <div className="text-3xl">{media.length}</div>
            </ContentCard>
            <ContentCard
                title="People"
                initialBadge={{
                    color: InitialColors.PEOPLE
                }}>
                <div className="text-3xl">{authors.size}</div>
            </ContentCard>

            <ContentCard className="col-span-1 col-start-6" title={<>Followers</>}>
                <div className="text-3xl font-medium">{currentFollowers}</div>
                {followersChange !== 0 ? (
                    <div
                        className={classNames('flex items-center', {
                            'text-red-700': followersChange < 0,
                            'text-green-700': followersChange > 0
                        })}>
                        {followersChange.toFixed(2)}% vs previous
                    </div>
                ) : null}
            </ContentCard>
        </div>
    );
}
