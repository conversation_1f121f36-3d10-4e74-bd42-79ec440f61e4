import useActivities from '@/api/hooks/useActivities';
import { useOrganisation } from '@/components/OrganisationProvider';
import hasField from '@/util/hasField';
import { sentimentScore } from '@/util/sentiment';
import { isDefined } from '@quarterback/util';
import { useMemo } from 'react';
import { DateRange } from 'react-day-picker';

function useStats(range: DateRange) {
    const organisation = useOrganisation();

    const { data: activities = [] } = useActivities(
        organisation.selected?.organisation,
        organisation.selected?.entity,
        range.from!,
        range.to!
    );

    const authors = useMemo(() => {
        return new Set(
            activities
                .map((activity) => {
                    switch (activity.type) {
                        case 'asx-announcement':
                            return undefined;
                        case 'news':
                            return activity.source.url;
                        default:
                            return activity.author?.userId;
                    }
                })
                .filter(isDefined)
        );
    }, [activities]);

    const averageSentiment = useMemo(() => {
        const agg = [...activities].filter(hasField('sentiment')).reduce(
            (agg, activity) => ({
                count: agg.count + 1,
                sum: agg.sum + sentimentScore(activity.sentiment)
            }),
            { count: 0, sum: 0 }
        );

        return agg.count > 0 ? agg.sum / agg.count : 0;
    }, [activities]);

    return {
        organisation,
        activities: activities,
        activitiesCount: activities.length,
        people: authors,
        peopleCount: authors.size,
        newPeople: 0,
        averageSentiment: averageSentiment,
        averageSentimentBySource: {}
    };
}

export default useStats;
