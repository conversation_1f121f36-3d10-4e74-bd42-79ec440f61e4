import { useMemo } from 'react';
import { formatWithTimeZone } from '@/util/date';
import { Activity } from '@quarterback/types';
import { groupBy } from '@quarterback/util';
import { scaleLinear, scaleTime } from '@visx/scale';
import { extent } from '@visx/vendor/d3-array';
import { Group } from '@visx/group';
import { LinePath } from '@visx/shape';
import { curveMonotoneX } from '@visx/curve';
import { AxisBottom, AxisLeft, Orientation } from '@visx/axis';
import { ParentSize } from '@visx/responsive';
import source from '@/util/source';
import { GridRows } from '@visx/grid';

const colors = {
    Hotcopper: '#FF6B35',
    Twitter: '#8B5CF6',
    LinkedIn: '#3B82F6',
    Reddit: '#F59E0B',
    ASX: '#10B981'
};

const mainSources = ['Hotcopper', 'Twitter', 'LinkedIn', 'Reddit', 'ASX'];

interface DataPoint {
    date: Date;
    [key: string]: number | Date;
}

interface SourceOverTimeChartProps {
    activities: Array<Activity>;
    width: number;
    height: number;
}

const SourceOverTimeChart = function SourceOverTimeChart({
    activities,
    width,
    height
}: SourceOverTimeChartProps) {
    const data = useMemo(() => {
        const dailyData = Object.entries(
            groupBy(activities, (it) => formatWithTimeZone(it.posted, 'yyyy-MM-dd'))
        )
            .sort((a, b) => new Date(a[0]).valueOf() - new Date(b[0]).valueOf())
            .map(([day, dayActivities]) => {
                const countBySource = Object.entries(
                    groupBy(dayActivities, (it) => source(it))
                ).reduce(
                    (acc, [sourceName, sourceActivities]) => ({
                        ...acc,
                        [sourceName]: sourceActivities.length
                    }),
                    {} as Record<string, number>
                );

                const dataPoint: DataPoint = {
                    date: new Date(day)
                };

                mainSources.forEach((sourceName) => {
                    dataPoint[sourceName] = countBySource[sourceName] || 0;
                });

                return dataPoint;
            });

        return dailyData;
    }, [activities]);

    const margin = { top: 20, right: 18, bottom: 25, left: 15 };
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const xScale = useMemo(() => {
        const dateExtent = extent(data, (d) => d.date) as [Date, Date];
        return scaleTime({
            domain: dateExtent,
            range: [0, innerWidth]
        });
    }, [data, innerWidth]);

    const yScale = useMemo(() => {
        const allValues = data.flatMap((d) =>
            mainSources.map((source) => d[source] as number)
        );
        const maxValue = allValues.length > 0 ? Math.max(...allValues) : 1;
        return scaleLinear({
            domain: [0, Math.max(maxValue, 8)],
            range: [innerHeight, 0]
        });
    }, [data, innerHeight]);

    const xTickFormat = (value: Date | { valueOf(): number }) => {
        if (value instanceof Date) {
            return formatWithTimeZone(value, 'MMM do');
        } else {
            return formatWithTimeZone(value.valueOf(), 'MMM do');
        }
    };

    const yTickFormat = (value: number | { valueOf(): number }) => {
        if (typeof value === 'number') {
            return value.toString();
        } else {
            return value.valueOf().toString();
        }
    };

    const AXIS_COLOR = '#94a3b8';

    // Handle empty data case
    if (!data || data.length === 0) {
        return (
            <div
                style={{
                    width: '100%',
                    height,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: '#6b7280',
                    fontSize: '14px'
                }}>
                No data available
            </div>
        );
    }

    return (
        <div style={{ width: '100%' }}>
            <svg width={width} height={height}>
                <Group left={margin.left} top={margin.top}>
                    <GridRows
                        scale={yScale}
                        width={innerWidth}
                        stroke="#e2e8f0"
                        strokeWidth={1}
                        strokeOpacity={0.8}
                        numTicks={Math.min(6, Math.ceil(yScale.domain()[1]))}
                    />

                    {mainSources.map((sourceName) => (
                        <LinePath
                            key={sourceName}
                            data={data}
                            x={(d) => xScale(d.date) ?? 0}
                            y={(d) => yScale(d[sourceName] as number) ?? 0}
                            stroke={colors[sourceName as keyof typeof colors]}
                            strokeWidth={2}
                            fill="none"
                            curve={curveMonotoneX}
                        />
                    ))}

                    {/* Axes */}
                    <AxisLeft
                        scale={yScale}
                        orientation={Orientation.left}
                        tickFormat={yTickFormat}
                        left={innerWidth + 18}
                        tickStroke={AXIS_COLOR}
                        stroke={AXIS_COLOR}
                        tickLabelProps={{ fill: AXIS_COLOR, fontSize: 12 }}
                    />
                    <AxisBottom
                        scale={xScale}
                        orientation={Orientation.bottom}
                        tickFormat={xTickFormat}
                        top={innerHeight}
                        tickStroke={AXIS_COLOR}
                        stroke={AXIS_COLOR}
                        numTicks={6}
                        tickLabelProps={{ fill: AXIS_COLOR, fontSize: 12 }}
                    />
                </Group>
            </svg>

            {/* Legend */}
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'center',
                    gap: '20px',
                    marginTop: '10px',
                    fontSize: '12px'
                }}>
                {mainSources.map((sourceName) => (
                    <div
                        key={sourceName}
                        style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                        <div
                            style={{
                                width: '12px',
                                height: '12px',
                                backgroundColor:
                                    colors[sourceName as keyof typeof colors],
                                borderRadius: '50%'
                            }}
                        />
                        <span>{sourceName}</span>
                    </div>
                ))}
            </div>
        </div>
    );
};

interface Props {
    activities: Array<Activity>;
    height?: number;
}

export default function SourceOverTime({ activities, height = 300 }: Props) {
    return (
        <div style={{ width: '100%', height }}>
            <ParentSize>
                {({ width }) => (
                    <SourceOverTimeChart
                        activities={activities}
                        width={width}
                        height={height}
                    />
                )}
            </ParentSize>
        </div>
    );
}
