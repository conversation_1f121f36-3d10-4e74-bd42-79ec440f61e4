import React from 'react';
import Initials, { InitialsProps } from '../Initials';
import classNames from 'classnames';

interface ContentCardProps {
    title: React.ReactNode;
    className?: string;
    childrenClassName?: string;
    initialBadge?: InitialsProps;
    children: React.ReactNode;
}

function ContentCard(props: ContentCardProps) {
    const {
        title,
        className = '',
        childrenClassName = '',
        initialBadge = {},
        children
    } = props;

    return (
        <div className={classNames('bg-white border rounded-lg p-4', className)}>
            <div className="flex gap-x-2 divide-gray-200 items-center">
                {typeof title === 'string' ? (
                    <Initials
                        name={initialBadge.name || title}
                        color={initialBadge.color}
                        hide={initialBadge.hide}
                    />
                ) : null}
                <div className="flex flex-col">
                    <span className="text-sm font-medium">{title}</span>
                </div>
            </div>
            <div className="w-full mt-2 border-b border-gray-200"></div>
            <div className={classNames('mt-4', childrenClassName)}>{children}</div>
        </div>
    );
}

export default ContentCard;
